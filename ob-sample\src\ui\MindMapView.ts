/**
 * 思维导图视图组件
 * 自定义Obsidian视图，集成思维导图渲染器
 */

import { ItemView, WorkspaceLeaf, MarkdownView, TFile } from 'obsidian';
import { MindMapRenderer } from '../core/MindMapRenderer';
import { VisJsRenderer } from '../core/VisJsRenderer';
import { MarkdownParser } from '../core/MarkdownParser';
import { ConnectionToolbar } from './ConnectionToolbar';
import { Logger } from '../utils';
import { MindMapAPI, ExtendedMindMapAPI, LogicalConnection, ConnectionMode, ConnectionEventType, ConnectionEventData } from '../types';
import MindMapSyncPlugin from '../../main';

export const MINDMAP_VIEW_TYPE = 'mindmap-view';

export class MindMapView extends ItemView {
  private plugin: MindMapSyncPlugin;
  private renderer: ExtendedMindMapAPI | null = null;
  private parser: MarkdownParser;
  private logger: Logger;
  private currentFile: string | null = null;
  private isUpdating = false;
  private connectionToolbar: ConnectionToolbar | null = null;
  
  constructor(leaf: WorkspaceLeaf, plugin: MindMapSyncPlugin) {
    super(leaf);
    this.plugin = plugin;
    this.logger = new Logger(plugin.settings.debugMode);
    this.parser = new MarkdownParser(this.logger);
  }
  
  getViewType(): string {
    return MINDMAP_VIEW_TYPE;
  }
  
  getDisplayText(): string {
    return '思维导图';
  }
  
  getIcon(): string {
    return 'git-branch';
  }
  
  async onOpen(): Promise<void> {
    this.logger.debug('思维导图视图打开');
    
    // 创建容器
    const container = this.containerEl.children[1] as HTMLElement;
    container.empty();
    container.addClass('mindmap-view-container');

    // 创建工具栏
    this.createToolbar(container);

    // 创建连接工具栏
    this.createConnectionToolbar(container);

    // 创建思维导图容器
    const mindmapContainer = container.createDiv('mindmap-container');
    mindmapContainer.style.height = 'calc(100% - 80px)'; // 调整高度以适应两个工具栏
    mindmapContainer.style.width = '100%';
    
    // 初始化渲染器
    this.renderer = new VisJsRenderer(mindmapContainer, this.logger);
    
    // 设置节点变更回调
    this.renderer.setNodeChangeCallback((nodeId, changes) => {
      this.handleNodeChange(nodeId, changes);
    });

    // 设置连接事件回调
    this.renderer.setConnectionEventCallback((event, data) => {
      this.handleConnectionEvent(event, data);
    });
    
    // 监听活动文件变化
    this.registerEvent(
      this.app.workspace.on('active-leaf-change', () => {
        this.updateFromActiveFile();
      })
    );
    
    // 监听文件内容变化
    this.registerEvent(
      this.app.workspace.on('editor-change', (editor, info) => {
        if (!this.isUpdating && this.plugin.settings.enableSync) {
          this.debounceUpdate();
        }
      })
    );
    
    // 初始加载
    this.updateFromActiveFile();
  }
  
  async onClose(): Promise<void> {
    this.logger.debug('思维导图视图关闭');
    
    if (this.renderer) {
      this.renderer.destroy();
      this.renderer = null;
    }
  }
  
  /**
   * 创建工具栏
   */
  private createToolbar(container: HTMLElement): void {
    const toolbar = container.createDiv('mindmap-toolbar');
    toolbar.style.height = '40px';
    toolbar.style.padding = '8px';
    toolbar.style.borderBottom = '1px solid var(--background-modifier-border)';
    toolbar.style.display = 'flex';
    toolbar.style.alignItems = 'center';
    toolbar.style.gap = '8px';
    
    // 刷新按钮
    const refreshBtn = toolbar.createEl('button', {
      text: '刷新',
      cls: 'mod-cta'
    });
    refreshBtn.addEventListener('click', () => {
      this.updateFromActiveFile();
    });
    
    // 同步开关
    const syncToggle = toolbar.createEl('button', {
      text: this.plugin.settings.enableSync ? '同步: 开' : '同步: 关',
      cls: this.plugin.settings.enableSync ? 'mod-cta' : ''
    });
    syncToggle.addEventListener('click', () => {
      this.plugin.settings.enableSync = !this.plugin.settings.enableSync;
      syncToggle.textContent = this.plugin.settings.enableSync ? '同步: 开' : '同步: 关';
      syncToggle.className = this.plugin.settings.enableSync ? 'mod-cta' : '';
      this.plugin.saveSettings();
    });
    
    // 居中按钮
    const centerBtn = toolbar.createEl('button', {
      text: '居中'
    });
    centerBtn.addEventListener('click', () => {
      if (this.renderer) {
        this.renderer.fit();
      }
    });
    
    // 文件名显示
    const fileLabel = toolbar.createEl('span', {
      cls: 'mindmap-file-label'
    });
    fileLabel.style.marginLeft = 'auto';
    fileLabel.style.fontSize = '12px';
    fileLabel.style.color = 'var(--text-muted)';
    
    this.updateFileLabel(fileLabel);
  }
  
  /**
   * 更新文件标签
   */
  private updateFileLabel(label: HTMLElement): void {
    const activeFile = this.app.workspace.getActiveFile();
    if (activeFile) {
      label.textContent = activeFile.basename;
      this.currentFile = activeFile.path;
    } else {
      label.textContent = '无活动文件';
      this.currentFile = null;
    }
  }
  
  /**
   * 从活动文件更新思维导图
   */
  private async updateFromActiveFile(): Promise<void> {
    const activeView = this.app.workspace.getActiveViewOfType(MarkdownView);
    if (!activeView || !this.renderer) {
      this.logger.debug('无活动Markdown视图或渲染器未初始化');
      return;
    }
    
    const file = activeView.file;
    if (!file) {
      this.logger.debug('无活动文件');
      return;
    }
    
    try {
      this.isUpdating = true;
      
      // 读取文件内容
      const content = await this.app.vault.read(file);
      
      // 解析并渲染
      const nodes = this.parser.parse(content);
      const markdown = this.parser.toMarkdown(nodes);
      
      this.renderer.render(markdown);

      // 加载连接数据
      this.loadConnectionsFromMarkdown();

      this.currentFile = file.path;
      this.logger.debug(`思维导图已更新: ${file.basename}`);

      // 更新文件标签
      const fileLabel = this.containerEl.querySelector('.mindmap-file-label') as HTMLElement;
      if (fileLabel) {
        this.updateFileLabel(fileLabel);
      }
      
    } catch (error) {
      this.logger.error('更新思维导图失败', error);
    } finally {
      this.isUpdating = false;
    }
  }
  
  /**
   * 防抖更新
   */
  private debounceUpdate = this.debounce(() => {
    this.updateFromActiveFile();
  }, 300); // 使用默认值，避免初始化问题
  
  /**
   * 防抖函数
   */
  private debounce<T extends (...args: any[]) => any>(
    func: T,
    wait: number
  ): (...args: Parameters<T>) => void {
    let timeout: NodeJS.Timeout;
    return (...args: Parameters<T>) => {
      clearTimeout(timeout);
      timeout = setTimeout(() => func.apply(this, args), wait);
    };
  }
  
  /**
   * 处理节点变更
   */
  private async handleNodeChange(nodeId: string, changes: any): Promise<void> {
    if (!this.plugin.settings.enableSync || this.isUpdating) {
      return;
    }
    
    this.logger.debug('处理节点变更', { nodeId, changes });
    
    try {
      this.isUpdating = true;
      
      // 获取当前活动的Markdown视图
      const activeView = this.app.workspace.getActiveViewOfType(MarkdownView);
      if (!activeView) {
        this.logger.warn('无活动Markdown视图，无法同步变更');
        return;
      }
      
      // 这里需要实现思维导图到Markdown的同步逻辑
      // 暂时只记录日志
      this.logger.debug('节点变更已记录，等待同步实现');
      
    } catch (error) {
      this.logger.error('处理节点变更失败', error);
    } finally {
      this.isUpdating = false;
    }
  }
  
  /**
   * 手动刷新思维导图
   */
  public async refresh(): Promise<void> {
    await this.updateFromActiveFile();
  }
  
  /**
   * 获取当前渲染器
   */
  public getRenderer(): MindMapAPI | null {
    return this.renderer;
  }
  
  /**
   * 获取当前解析器
   */
  public getParser(): MarkdownParser {
    return this.parser;
  }

  // ==================== 连接功能相关方法 ====================

  /**
   * 创建连接工具栏
   */
  private createConnectionToolbar(container: HTMLElement): void {
    // 检查是否启用连接功能
    if (!this.plugin.settings.enableLogicalConnections) {
      return;
    }

    const toolbarContainer = container.createDiv('connection-toolbar-container');

    this.connectionToolbar = new ConnectionToolbar(toolbarContainer, this.logger, {
      onModeChange: (mode: ConnectionMode) => {
        this.handleConnectionModeChange(mode);
      },
      onStyleChange: (style) => {
        this.logger.debug('连接样式变更', style);
      },
      onColorChange: (color) => {
        this.logger.debug('连接颜色变更', color);
      },
      onWidthChange: (width) => {
        this.logger.debug('连接宽度变更', width);
      },
      onArrowTypeChange: (arrowType) => {
        this.logger.debug('箭头类型变更', arrowType);
      },
      onLabelChange: (label) => {
        this.logger.debug('连接标签变更', label);
      }
    });
  }

  /**
   * 处理连接模式变更
   */
  private handleConnectionModeChange(mode: ConnectionMode): void {
    if (this.renderer) {
      this.renderer.setConnectionMode(mode);
    }

    this.logger.debug('连接模式变更', mode);
  }

  /**
   * 处理连接事件
   */
  private handleConnectionEvent(event: ConnectionEventType, data: ConnectionEventData): void {
    this.logger.debug('连接事件', { event, data });

    switch (event) {
      case 'connection:create':
        this.onConnectionCreated(data);
        break;
      case 'connection:update':
        this.onConnectionUpdated(data);
        break;
      case 'connection:delete':
        this.onConnectionDeleted(data);
        break;
      case 'connection:select':
        this.onConnectionSelected(data);
        break;
    }
  }

  /**
   * 连接创建事件处理
   */
  private onConnectionCreated(data: ConnectionEventData): void {
    // 保存连接到Markdown
    this.saveConnectionsToMarkdown();

    this.logger.info('连接已创建', data.connectionId);
  }

  /**
   * 连接更新事件处理
   */
  private onConnectionUpdated(data: ConnectionEventData): void {
    // 保存连接到Markdown
    this.saveConnectionsToMarkdown();

    this.logger.info('连接已更新', data.connectionId);
  }

  /**
   * 连接删除事件处理
   */
  private onConnectionDeleted(data: ConnectionEventData): void {
    // 保存连接到Markdown
    this.saveConnectionsToMarkdown();

    this.logger.info('连接已删除', data.connectionId);
  }

  /**
   * 连接选择事件处理
   */
  private onConnectionSelected(data: ConnectionEventData): void {
    this.logger.debug('连接已选择', data.connectionId);
  }

  /**
   * 保存连接到Markdown
   */
  private async saveConnectionsToMarkdown(): Promise<void> {
    if (!this.renderer || !this.currentFile || this.isUpdating) {
      return;
    }

    try {
      this.isUpdating = true;

      // 获取当前文件
      const file = this.app.vault.getAbstractFileByPath(this.currentFile);
      if (!file || file.path !== this.currentFile) {
        return;
      }

      // 确保是TFile类型
      if (!(file instanceof TFile)) {
        return;
      }

      // 读取当前内容
      const content = await this.app.vault.read(file);

      // 解析当前内容
      const nodes = this.parser.parse(content);

      // 获取当前连接
      const connections = this.renderer.getLogicalConnections();

      // 设置连接到解析器
      this.parser.setConnections(connections);

      // 生成新的Markdown内容
      const newContent = this.parser.toMarkdown(nodes);

      // 保存文件
      await this.app.vault.modify(file, newContent);

      this.logger.debug('连接数据已保存到Markdown');

    } catch (error) {
      this.logger.error('保存连接数据失败', error);
    } finally {
      this.isUpdating = false;
    }
  }

  /**
   * 从Markdown智能加载连接（增量更新）
   */
  private loadConnectionsFromMarkdown(): void {
    if (!this.renderer) {
      return;
    }

    try {
      const newConnections = this.parser.getConnections();

      // 标准化连接ID，确保稳定性
      this.normalizeConnectionIds(newConnections);

      // 智能增量更新
      this.updateConnectionsIncrementally(newConnections);

      this.logger.debug(`智能更新连接完成，当前总数: ${newConnections.length}`);
    } catch (error) {
      this.logger.error('加载连接数据失败', error);
    }
  }

  /**
   * 标准化连接ID，确保稳定性
   */
  private normalizeConnectionIds(connections: LogicalConnection[]): void {
    connections.forEach(conn => {
      // 如果ID不是稳定格式，重新生成
      if (!conn.id || !conn.id.match(/^conn-\w+-\w+$/)) {
        conn.id = `conn-${conn.fromNodeId}-${conn.toNodeId}`;
        this.logger.debug(`标准化连接ID: ${conn.fromNodeId} -> ${conn.toNodeId} = ${conn.id}`);
      }
    });
  }

  /**
   * 智能增量更新连接
   */
  private updateConnectionsIncrementally(newConnections: LogicalConnection[]): void {
    const existingConnections = new Map(
      this.renderer!.getLogicalConnections().map(c => [c.id, c])
    );
    const newConnectionMap = new Map(newConnections.map(c => [c.id, c]));

    let addedCount = 0;
    let removedCount = 0;
    let unchangedCount = 0;

    // 删除不存在的连接
    for (const [id, conn] of existingConnections) {
      if (!newConnectionMap.has(id)) {
        this.renderer!.removeLogicalConnection(id);
        removedCount++;
        this.logger.debug(`删除连接: ${id}`);
      } else {
        unchangedCount++;
      }
    }

    // 添加新连接
    for (const [id, conn] of newConnectionMap) {
      if (!existingConnections.has(id)) {
        this.renderer!.addLogicalConnection(conn);
        addedCount++;
        this.logger.debug(`添加连接: ${id}`);
      }
    }

    // 只在有变化时输出日志
    if (addedCount > 0 || removedCount > 0) {
      this.logger.info(`连接更新完成: +${addedCount}, -${removedCount}, =${unchangedCount}`);
    } else {
      this.logger.debug(`连接无变化: ${unchangedCount} 个连接保持不变`);
    }
  }
}
