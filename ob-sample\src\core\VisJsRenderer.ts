/**
 * Vis.js思维导图渲染器
 * 基于Vis.js Network实现高性能思维导图渲染和交互
 */

import { Network, Node, Edge, Options } from 'vis-network';
import { MindMapAPI, NodeData, LogicalConnection, ExtendedMindMapAPI, ConnectionMode, ConnectionEventType, ConnectionEventData } from '../types';
import { Logger } from '../utils';

interface VisNode extends Node {
  id: string;
  label: string;
  level: number;
  type: string;
  group: string;
  originalData: NodeData;
}

interface VisEdge extends Edge {
  id?: string;
  from: string;
  to: string;
}

// 增强的节点数据接口
interface EnhancedNodeData {
  id: string;
  content: string;
  rawContent: string;        // 原始内容（含样式标记）
  level: number;             // 精确层级
  type: 'heading' | 'list' | 'code' | 'quote' | 'text';
  headingLevel?: number;     // H1-H6层级
  listLevel?: number;        // 列表嵌套层级
  indentLevel?: number;      // 缩进层级
  styles: StyleInfo;         // 样式信息
  children: EnhancedNodeData[];
  parent?: EnhancedNodeData;
  position: { line: number; ch: number };
}

// 样式信息接口
interface StyleInfo {
  bold: boolean;
  italic: boolean;
  code: boolean;
  link?: string;
  emoji: string[];
  strikethrough: boolean;
  highlight: boolean;
}

// 行结构分析接口
interface LineStructure {
  level: number;
  type: 'heading' | 'list' | 'code' | 'quote' | 'text';
  headingLevel: number;
  listLevel: number;
  indentLevel: number;
  content: string;
  rawContent: string;
  styles: StyleInfo;
}

export class VisJsRenderer implements ExtendedMindMapAPI {
  private container: HTMLElement;
  private network: Network | null = null;
  private nodes: any;
  private edges: any;
  private logicalConnections: any; // vis-data DataSet for logical connections
  private logger: Logger;
  private onNodeChangeCallback?: (nodeId: string, changes: any) => void;
  private onConnectionEventCallback?: (event: ConnectionEventType, data: ConnectionEventData) => void;
  private nodeMap: Map<string, NodeData> = new Map();
  private enhancedNodeMap: Map<string, EnhancedNodeData> = new Map();
  private connectionMap: Map<string, LogicalConnection> = new Map();
  private themeObserver: MutationObserver | null = null;
  private connectionMode: ConnectionMode = 'normal';
  private selectedSourceNode: string | null = null;

  // 待处理连接管理机制
  private pendingConnections: Map<string, LogicalConnection> = new Map();

  // 连接处理统计信息
  private connectionProcessingStats = {
    formatValidationPassed: 0,
    businessValidationPassed: 0,
    pendingCount: 0,
    processedCount: 0
  };

  constructor(container: HTMLElement, logger: Logger) {
    this.container = container;
    this.logger = logger;

    // 动态导入DataSet
    const visData = require('vis-data');
    this.nodes = new visData.DataSet();
    this.edges = new visData.DataSet();
    this.logicalConnections = new visData.DataSet();

    this.initializeNetwork();
    this.setupThemeListener();
  }

  /**
   * 检测当前Obsidian主题
   */
  private detectCurrentTheme(): boolean {
    try {
      return document.body.classList.contains('theme-dark');
    } catch (error) {
      this.logger.error('主题检测失败', error);
      return false; // 默认为明亮主题
    }
  }

  /**
   * 解析CSS变量为实际颜色值
   */
  private resolveCSSVariable(cssValue: string): string {
    try {
      // 如果不是CSS变量，直接返回
      if (!cssValue.startsWith('var(')) {
        return cssValue;
      }

      // 创建临时元素来获取计算后的样式
      const tempElement = document.createElement('div');
      tempElement.style.color = cssValue;
      document.body.appendChild(tempElement);

      const computedStyle = window.getComputedStyle(tempElement);
      const resolvedColor = computedStyle.color;

      document.body.removeChild(tempElement);

      // 如果解析失败，返回默认颜色
      if (!resolvedColor || resolvedColor === cssValue) {
        this.logger.warn(`CSS变量解析失败: ${cssValue}, 使用默认颜色`);
        return this.getDefaultColorForVariable(cssValue);
      }

      return resolvedColor;
    } catch (error) {
      this.logger.error('CSS变量解析错误', error);
      return this.getDefaultColorForVariable(cssValue);
    }
  }

  /**
   * 获取CSS变量的默认颜色
   */
  private getDefaultColorForVariable(cssVar: string): string {
    const isDark = this.detectCurrentTheme();

    const lightColors: { [key: string]: string } = {
      'var(--background-secondary)': '#f5f5f5',
      'var(--background-primary)': '#ffffff',
      'var(--text-normal)': '#2e3338',
      'var(--text-muted)': '#888888',
      'var(--text-accent)': '#7c3aed',
      'var(--color-green)': '#22c55e',
      'var(--background-primary-alt)': '#f8f9fa',
      'var(--background-modifier-border)': '#e1e4e8',
      'var(--font-text)': 'system-ui, -apple-system, sans-serif',
      'var(--font-monospace)': 'Monaco, Consolas, monospace'
    };

    const darkColors: { [key: string]: string } = {
      'var(--background-secondary)': '#2d3748',
      'var(--background-primary)': '#1a202c',
      'var(--text-normal)': '#e2e8f0',
      'var(--text-muted)': '#a0aec0',
      'var(--text-accent)': '#9f7aea',
      'var(--color-green)': '#48bb78',
      'var(--background-primary-alt)': '#2d3748',
      'var(--background-modifier-border)': '#4a5568',
      'var(--font-text)': 'system-ui, -apple-system, sans-serif',
      'var(--font-monospace)': 'Monaco, Consolas, monospace'
    };

    const colors = isDark ? darkColors : lightColors;
    return colors[cssVar] || (isDark ? '#e2e8f0' : '#333333');
  }

  /**
   * 获取指定主题的颜色方案
   */
  private getColorSchemeForTheme(isDark: boolean) {
    try {
      if (isDark) {
        return this.getDarkThemeColors();
      } else {
        return this.getLightThemeColors();
      }
    } catch (error) {
      this.logger.error('获取主题颜色方案失败', error);
      return this.getFallbackColors();
    }
  }

  /**
   * 处理颜色配置，解析CSS变量
   */
  private processColorConfig(config: any): any {
    const processed = JSON.parse(JSON.stringify(config)); // 深拷贝

    // 递归处理所有颜色相关属性
    const processObject = (obj: any) => {
      for (const key in obj) {
        if (typeof obj[key] === 'string') {
          // 解析CSS变量
          if (obj[key].startsWith('var(')) {
            obj[key] = this.resolveCSSVariable(obj[key]);
          }
        } else if (typeof obj[key] === 'object' && obj[key] !== null) {
          processObject(obj[key]);
        }
      }
    };

    processObject(processed);
    return processed;
  }

  /**
   * 获取暗黑主题颜色配置
   */
  private getDarkThemeColors() {
    const rawConfig = {
      heading1: {
        color: { background: 'rgba(33, 150, 243, 0.25)', border: '#42a5f5' },
        font: { size: 18, face: 'var(--font-text)', color: '#90caf9' },
        shape: 'box',
        margin: { top: 15, right: 15, bottom: 15, left: 15 }
      },
      heading2: {
        color: { background: 'rgba(156, 39, 176, 0.25)', border: '#ab47bc' },
        font: { size: 16, face: 'var(--font-text)', color: '#ce93d8' },
        shape: 'box',
        margin: { top: 12, right: 12, bottom: 12, left: 12 }
      },
      heading3: {
        color: { background: 'rgba(76, 175, 80, 0.25)', border: '#66bb6a' },
        font: { size: 14, face: 'var(--font-text)', color: '#a5d6a7' },
        shape: 'box',
        margin: { top: 10, right: 10, bottom: 10, left: 10 }
      },
      list: {
        color: { background: 'var(--background-secondary)', border: 'var(--text-muted)' },
        font: { size: 12, face: 'var(--font-text)', color: 'var(--text-normal)' },
        shape: 'ellipse',
        margin: { top: 8, right: 8, bottom: 8, left: 8 }
      },
      quote: {
        color: { background: 'rgba(255, 152, 0, 0.25)', border: '#ffb74d' },
        font: { size: 12, face: 'var(--font-text)', color: '#ffcc02', style: 'italic' },
        shape: 'box',
        margin: { top: 8, right: 8, bottom: 8, left: 8 }
      },
      code: {
        color: { background: 'var(--background-primary-alt)', border: 'var(--text-accent)' },
        font: { size: 11, face: 'var(--font-monospace)', color: 'var(--text-normal)' },
        shape: 'box',
        margin: { top: 6, right: 6, bottom: 6, left: 6 }
      },
      'ai-generated': {
        color: { background: 'rgba(34, 197, 94, 0.25)', border: 'var(--color-green)' },
        font: { size: 12, face: 'var(--font-text)', color: '#4ade80' },
        shape: 'ellipse'
      }
    };

    // 处理CSS变量解析
    return this.processColorConfig(rawConfig);
  }

  /**
   * 获取明亮主题颜色配置
   */
  private getLightThemeColors() {
    const rawConfig = {
      heading1: {
        color: { background: '#e3f2fd', border: '#1976d2' },
        font: { size: 18, face: 'var(--font-text)', color: '#1565c0' },
        shape: 'box',
        margin: { top: 15, right: 15, bottom: 15, left: 15 }
      },
      heading2: {
        color: { background: '#f3e5f5', border: '#7b1fa2' },
        font: { size: 16, face: 'var(--font-text)', color: '#6a1b9a' },
        shape: 'box',
        margin: { top: 12, right: 12, bottom: 12, left: 12 }
      },
      heading3: {
        color: { background: '#e8f5e8', border: '#388e3c' },
        font: { size: 14, face: 'var(--font-text)', color: '#2e7d32' },
        shape: 'box',
        margin: { top: 10, right: 10, bottom: 10, left: 10 }
      },
      list: {
        color: { background: 'var(--background-secondary)', border: 'var(--text-muted)' },
        font: { size: 12, face: 'var(--font-text)', color: 'var(--text-normal)' },
        shape: 'ellipse',
        margin: { top: 8, right: 8, bottom: 8, left: 8 }
      },
      quote: {
        color: { background: '#fff3e0', border: '#f57c00' },
        font: { size: 12, face: 'var(--font-text)', color: '#e65100', style: 'italic' },
        shape: 'box',
        margin: { top: 8, right: 8, bottom: 8, left: 8 }
      },
      code: {
        color: { background: 'var(--background-primary-alt)', border: 'var(--text-accent)' },
        font: { size: 11, face: 'var(--font-monospace)', color: 'var(--text-normal)' },
        shape: 'box',
        margin: { top: 6, right: 6, bottom: 6, left: 6 }
      },
      'ai-generated': {
        color: { background: 'rgba(34, 197, 94, 0.1)', border: 'var(--color-green)' },
        font: { size: 12, face: 'var(--font-text)', color: 'var(--text-normal)' },
        shape: 'ellipse'
      }
    };

    // 处理CSS变量解析
    return this.processColorConfig(rawConfig);
  }

  /**
   * 计算颜色的相对亮度
   * @param color 十六进制颜色值，如 '#ffffff' 或 'rgba(255,255,255,1)'
   */
  private calculateLuminance(color: string): number {
    try {
      let r: number, g: number, b: number;

      // 处理十六进制颜色
      if (color.startsWith('#')) {
        const hex = color.slice(1);
        r = parseInt(hex.slice(0, 2), 16);
        g = parseInt(hex.slice(2, 4), 16);
        b = parseInt(hex.slice(4, 6), 16);
      }
      // 处理rgba颜色
      else if (color.startsWith('rgba')) {
        const match = color.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)/);
        if (!match) return 0.5; // 默认中等亮度
        r = parseInt(match[1]);
        g = parseInt(match[2]);
        b = parseInt(match[3]);
      }
      // 处理rgb颜色
      else if (color.startsWith('rgb')) {
        const match = color.match(/rgb\((\d+),\s*(\d+),\s*(\d+)/);
        if (!match) return 0.5;
        r = parseInt(match[1]);
        g = parseInt(match[2]);
        b = parseInt(match[3]);
      }
      else {
        // 对于CSS变量或其他格式，返回默认值
        return 0.5;
      }

      // 转换为相对亮度值 (0-1)
      const rsRGB = r / 255;
      const gsRGB = g / 255;
      const bsRGB = b / 255;

      const rLinear = rsRGB <= 0.03928 ? rsRGB / 12.92 : Math.pow((rsRGB + 0.055) / 1.055, 2.4);
      const gLinear = gsRGB <= 0.03928 ? gsRGB / 12.92 : Math.pow((gsRGB + 0.055) / 1.055, 2.4);
      const bLinear = bsRGB <= 0.03928 ? bsRGB / 12.92 : Math.pow((bsRGB + 0.055) / 1.055, 2.4);

      return 0.2126 * rLinear + 0.7152 * gLinear + 0.0722 * bLinear;
    } catch (error) {
      this.logger.error('计算颜色亮度失败', error);
      return 0.5; // 返回默认中等亮度
    }
  }

  /**
   * 确保颜色对比度符合WCAG AA标准 (4.5:1)
   * @param textColor 文字颜色
   * @param backgroundColor 背景颜色
   * @param targetRatio 目标对比度，默认4.5
   */
  private ensureContrastRatio(textColor: string, backgroundColor: string, targetRatio: number = 4.5): string {
    try {
      const textLuminance = this.calculateLuminance(textColor);
      const bgLuminance = this.calculateLuminance(backgroundColor);

      // 计算当前对比度
      const lighter = Math.max(textLuminance, bgLuminance);
      const darker = Math.min(textLuminance, bgLuminance);
      const currentRatio = (lighter + 0.05) / (darker + 0.05);

      // 如果对比度已经足够，直接返回原颜色
      if (currentRatio >= targetRatio) {
        return textColor;
      }

      // 如果对比度不够，调整文字颜色
      const isDark = this.detectCurrentTheme();

      if (isDark) {
        // 暗黑主题：如果对比度不够，使用更亮的文字颜色
        if (textColor.startsWith('#')) {
          return this.lightenColor(textColor, 0.3);
        }
        return '#ffffff'; // 默认白色
      } else {
        // 明亮主题：如果对比度不够，使用更暗的文字颜色
        if (textColor.startsWith('#')) {
          return this.darkenColor(textColor, 0.3);
        }
        return '#000000'; // 默认黑色
      }
    } catch (error) {
      this.logger.error('确保对比度失败', error);
      // 返回安全的默认颜色
      return this.detectCurrentTheme() ? '#ffffff' : '#000000';
    }
  }

  /**
   * 加亮颜色
   */
  private lightenColor(color: string, amount: number): string {
    if (!color.startsWith('#')) return color;

    const hex = color.slice(1);
    const r = Math.min(255, parseInt(hex.slice(0, 2), 16) + Math.round(255 * amount));
    const g = Math.min(255, parseInt(hex.slice(2, 4), 16) + Math.round(255 * amount));
    const b = Math.min(255, parseInt(hex.slice(4, 6), 16) + Math.round(255 * amount));

    return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
  }

  /**
   * 加深颜色
   */
  private darkenColor(color: string, amount: number): string {
    if (!color.startsWith('#')) return color;

    const hex = color.slice(1);
    const r = Math.max(0, parseInt(hex.slice(0, 2), 16) - Math.round(255 * amount));
    const g = Math.max(0, parseInt(hex.slice(2, 4), 16) - Math.round(255 * amount));
    const b = Math.max(0, parseInt(hex.slice(4, 6), 16) - Math.round(255 * amount));

    return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
  }

  /**
   * 获取安全回退颜色配置
   */
  private getFallbackColors() {
    this.logger.warn('使用安全回退颜色配置');
    const rawConfig = {
      heading1: {
        color: { background: 'var(--background-secondary)', border: 'var(--text-accent)' },
        font: { size: 18, face: 'var(--font-text)', color: 'var(--text-normal)' },
        shape: 'box',
        margin: { top: 15, right: 15, bottom: 15, left: 15 }
      },
      heading2: {
        color: { background: 'var(--background-secondary)', border: 'var(--text-accent)' },
        font: { size: 16, face: 'var(--font-text)', color: 'var(--text-normal)' },
        shape: 'box',
        margin: { top: 12, right: 12, bottom: 12, left: 12 }
      },
      heading3: {
        color: { background: 'var(--background-secondary)', border: 'var(--text-accent)' },
        font: { size: 14, face: 'var(--font-text)', color: 'var(--text-normal)' },
        shape: 'box',
        margin: { top: 10, right: 10, bottom: 10, left: 10 }
      },
      list: {
        color: { background: 'var(--background-secondary)', border: 'var(--text-muted)' },
        font: { size: 12, face: 'var(--font-text)', color: 'var(--text-normal)' },
        shape: 'ellipse',
        margin: { top: 8, right: 8, bottom: 8, left: 8 }
      },
      quote: {
        color: { background: 'var(--background-secondary)', border: 'var(--text-muted)' },
        font: { size: 12, face: 'var(--font-text)', color: 'var(--text-normal)', style: 'italic' },
        shape: 'box',
        margin: { top: 8, right: 8, bottom: 8, left: 8 }
      },
      code: {
        color: { background: 'var(--background-primary-alt)', border: 'var(--text-accent)' },
        font: { size: 11, face: 'var(--font-monospace)', color: 'var(--text-normal)' },
        shape: 'box',
        margin: { top: 6, right: 6, bottom: 6, left: 6 }
      },
      'ai-generated': {
        color: { background: 'var(--background-secondary)', border: 'var(--text-accent)' },
        font: { size: 12, face: 'var(--font-text)', color: 'var(--text-normal)' },
        shape: 'ellipse'
      }
    };

    // 处理CSS变量解析
    return this.processColorConfig(rawConfig);
  }

  /**
   * 验证节点配置
   */
  private validateNodeConfig(nodeConfig: any): boolean {
    try {
      if (!nodeConfig || typeof nodeConfig !== 'object') {
        return false;
      }

      // 检查必需的属性
      const requiredProps = ['shape', 'font', 'borderWidth'];
      for (const prop of requiredProps) {
        if (!(prop in nodeConfig)) {
          this.logger.warn(`节点配置缺少必需属性: ${prop}`);
          return false;
        }
      }

      // 验证font配置
      if (nodeConfig.font && typeof nodeConfig.font === 'object') {
        if (!nodeConfig.font.size || !nodeConfig.font.color) {
          this.logger.warn('节点font配置不完整');
          return false;
        }
      }

      return true;
    } catch (error) {
      this.logger.error('验证节点配置失败', error);
      return false;
    }
  }

  /**
   * 验证groups配置
   */
  private validateGroupsConfig(groupsConfig: any): boolean {
    try {
      if (!groupsConfig || typeof groupsConfig !== 'object') {
        return false;
      }

      // 检查每个group的配置
      for (const groupName in groupsConfig) {
        const group = groupsConfig[groupName];

        if (!group || typeof group !== 'object') {
          this.logger.warn(`Group配置无效: ${groupName}`);
          return false;
        }

        // 检查必需的属性
        if (!group.color || !group.font || !group.shape) {
          this.logger.warn(`Group配置不完整: ${groupName}`);
          return false;
        }

        // 验证color配置
        if (typeof group.color === 'object') {
          if (!group.color.background) {
            this.logger.warn(`Group缺少background颜色: ${groupName}`);
            return false;
          }
        }

        // 验证font配置
        if (typeof group.font === 'object') {
          if (!group.font.size || !group.font.color) {
            this.logger.warn(`Group font配置不完整: ${groupName}`);
            return false;
          }
        }
      }

      return true;
    } catch (error) {
      this.logger.error('验证groups配置失败', error);
      return false;
    }
  }

  /**
   * 获取主题适配的颜色配置
   */
  private getThemeAwareColors() {
    try {
      const isDark = this.detectCurrentTheme();
      const colors = this.getColorSchemeForTheme(isDark);

      // 验证颜色配置是否有效
      if (!colors || typeof colors !== 'object') {
        throw new Error('颜色配置无效');
      }

      // 验证groups配置格式
      if (!this.validateGroupsConfig(colors)) {
        throw new Error('Groups配置验证失败');
      }

      return colors;
    } catch (error) {
      this.logger.error('获取主题适配颜色失败，使用安全回退配置', error);
      return this.getFallbackColors();
    }
  }

  /**
   * 设置主题变化监听器
   */
  private setupThemeListener(): void {
    try {
      // 监听body类名变化，检测主题切换
      this.themeObserver = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
            this.logger.debug('检测到主题变化，重新渲染思维导图');
            this.onThemeChange();
          }
        });
      });

      this.themeObserver.observe(document.body, {
        attributes: true,
        attributeFilter: ['class']
      });

      this.logger.debug('主题监听器设置完成');
    } catch (error) {
      this.logger.error('设置主题监听器失败', error);
    }
  }

  /**
   * 主题变化处理
   */
  private onThemeChange(): void {
    try {
      this.logger.debug('开始处理主题变化');

      if (!this.network) {
        this.logger.warn('网络未初始化，跳过主题变化处理');
        return;
      }

      // 获取新的主题配置
      const newOptions = this.getNetworkOptions();

      // 验证新配置
      if (!newOptions || !newOptions.groups) {
        this.logger.error('新主题配置无效，跳过更新');
        return;
      }

      // 尝试更新网络选项
      try {
        this.network.setOptions(newOptions);
        this.logger.debug('主题配置更新成功');
      } catch (setOptionsError) {
        this.logger.warn('setOptions失败，尝试重新初始化网络', setOptionsError);

        // 如果setOptions失败，尝试重新初始化
        this.initializeNetwork();

        // 如果有数据，重新渲染
        if (this.nodes && this.nodes.length > 0) {
          this.logger.debug('主题变化后重新渲染现有数据');
        }
      }

    } catch (error) {
      this.handleNetworkError(error, '主题变化处理');
    }
  }

  /**
   * 获取网络配置选项
   */
  private getNetworkOptions(): Options {
    try {
      const themeColors = this.getThemeAwareColors();

      // 构建节点配置
      const nodeConfig = {
        shape: 'box',
        margin: { top: 10, right: 10, bottom: 10, left: 10 },
        font: {
          size: 14,
          face: 'var(--font-text)',
          color: 'var(--text-normal)'
        },
        borderWidth: 2,
        shadow: {
          enabled: true,
          color: 'rgba(0,0,0,0.1)',
          size: 5,
          x: 2,
          y: 2
        },
        chosen: {
          node: (values: any, _id: string, selected: boolean, hovering: boolean) => {
            if (selected || hovering) {
              values.borderWidth = 3;
              // 安全地设置shadow属性
              if (typeof values.shadow === 'object' && values.shadow !== null) {
                values.shadow.size = 8;
              } else {
                // 如果shadow不是对象，创建一个新的shadow对象
                values.shadow = {
                  enabled: true,
                  size: 8,
                  x: 2,
                  y: 2,
                  color: 'rgba(0,0,0,0.2)'
                };
              }
            }
          },
          label: false
        }
      };

      // 验证节点配置
      if (!this.validateNodeConfig(nodeConfig)) {
        this.logger.warn('节点配置验证失败，使用简化配置');
        (nodeConfig as any).chosen = false; // 禁用复杂的chosen配置
      }

      return {
      layout: {
        hierarchical: {
          enabled: true,
          direction: 'LR',              // 从左到右布局
          sortMethod: 'hubsize',        // 按连接数排序，适合树状结构
          shakeTowards: 'roots',        // 向根节点收缩
          levelSeparation: 150,         // 层级间距优化
          nodeSpacing: 100,             // 节点间距优化
          treeSpacing: 200,             // 树间距
          blockShifting: true,          // 启用块移动优化
          edgeMinimization: true,       // 启用边最小化
          parentCentralization: true    // 父节点居中
        }
      },
      physics: {
        enabled: false // 禁用物理引擎，使用层次布局
      },
      nodes: nodeConfig,
      edges: {
        arrows: {
          to: { enabled: false }
        },
        color: {
          color: 'var(--text-muted)',
          opacity: 0.8                     // 提高透明度
        },
        width: 2,
        smooth: {
          enabled: true,
          type: 'cubicBezier',
          roundness: 0.4                   // 调整曲线
        },
        length: 150                        // 设置理想长度
      },
      groups: themeColors,  // 使用主题适配的颜色配置
      interaction: {
        dragNodes: true,
        dragView: true,
        zoomView: true,
        selectConnectedEdges: false,
        hover: true,
        hoverConnectedEdges: false
      }
    };
    } catch (error) {
      this.logger.error('获取网络配置选项失败，使用安全回退配置', error);
      const fallbackColors = this.getFallbackColors();

      return {
        layout: {
          hierarchical: {
            enabled: true,
            direction: 'LR',
            sortMethod: 'hubsize',        // 修复：与主配置保持一致，使用VisJS支持的有效值
            shakeTowards: 'roots',
            levelSeparation: 150,
            nodeSpacing: 100,
            treeSpacing: 200,
            blockShifting: true,
            edgeMinimization: true,
            parentCentralization: true
          }
        },
        physics: { enabled: false },
        nodes: {
          shape: 'box',
          margin: { top: 10, right: 10, bottom: 10, left: 10 },
          font: { size: 14, face: 'var(--font-text)', color: 'var(--text-normal)' },
          borderWidth: 2,
          shadow: { enabled: true, color: 'rgba(0,0,0,0.1)', size: 5, x: 2, y: 2 },
          chosen: {
            node: (values: any, _id: string, selected: boolean, hovering: boolean) => {
              if (selected || hovering) {
                values.borderWidth = 3;
                // 安全地设置shadow属性
                if (typeof values.shadow === 'object' && values.shadow !== null) {
                  values.shadow.size = 8;
                } else {
                  // 如果shadow不是对象，创建一个新的shadow对象
                  values.shadow = {
                    enabled: true,
                    size: 8,
                    x: 2,
                    y: 2,
                    color: 'rgba(0,0,0,0.2)'
                  };
                }
              }
            },
            label: false
          }
        },
        edges: {
          arrows: { to: { enabled: false } },
          color: { color: 'var(--text-muted)', opacity: 0.8 },
          width: 2,
          smooth: { enabled: true, type: 'cubicBezier', roundness: 0.4 },
          length: 150
        },
        groups: fallbackColors,
        interaction: {
          dragNodes: true,
          dragView: true,
          zoomView: true,
          selectConnectedEdges: false,
          hover: true,
          hoverConnectedEdges: false
        }
      };
    }
  }

  /**
   * 处理网络错误
   */
  private handleNetworkError(error: any, context: string): void {
    this.logger.error(`网络错误 [${context}]`, error);

    // 尝试恢复基本功能
    try {
      if (this.container) {
        this.container.innerHTML = '<div style="padding: 20px; text-align: center; color: var(--text-muted);">思维导图加载失败，请刷新页面重试</div>';
      }
    } catch (recoveryError) {
      this.logger.error('错误恢复失败', recoveryError);
    }
  }

  /**
   * 初始化网络
   */
  private initializeNetwork(): void {
    try {
      // 清空容器
      this.container.innerHTML = '';

      // 获取配置选项
      const options = this.getNetworkOptions();

      // 验证必要的数据结构
      if (!this.nodes || !this.edges) {
        throw new Error('节点或边数据未初始化');
      }

      // 创建网络
      this.network = new Network(this.container, {
        nodes: this.nodes,
        edges: this.edges
      }, options);

      // 设置事件监听
      this.setupEventListeners();

      this.logger.debug('网络初始化成功');
    } catch (error) {
      this.handleNetworkError(error, '初始化网络');
    }
  }

  private setupEventListeners(): void {
    if (!this.network) return;

    // 节点点击事件
    this.network.on('click', (params) => {
      if (params.nodes.length > 0) {
        const nodeId = params.nodes[0];
        this.handleNodeClick(nodeId);
      }
    });

    // 节点双击事件
    this.network.on('doubleClick', (params) => {
      if (params.nodes.length > 0) {
        const nodeId = params.nodes[0];
        this.handleNodeEdit(nodeId);
      }
    });

    // 节点悬停事件
    this.network.on('hoverNode', (params) => {
      this.handleNodeHover(params.node, true);
    });

    this.network.on('blurNode', (params) => {
      this.handleNodeHover(params.node, false);
    });

    // 拖拽结束事件
    this.network.on('dragEnd', (params) => {
      if (params.nodes.length > 0) {
        this.handleNodeDrag(params.nodes[0], params.pointer.canvas);
      }
    });
  }

  render(markdown: string): void {
    this.logger.debug('开始使用Vis.js渲染思维导图');
    this.logger.debug('输入Markdown内容:', markdown);

    try {
      // 解析Markdown为节点数据
      const nodeData = this.parseMarkdownToNodes(markdown);
      this.logger.debug('解析得到节点数:', nodeData.length);
      this.logger.debug('节点数据结构:', nodeData);

      if (nodeData.length === 0) {
        this.logger.debug('没有解析到任何节点，显示空状态');
        this.renderEmptyState();
        return;
      }

      // 转换为Vis.js格式
      const { visNodes, visEdges } = this.convertToVisFormat(nodeData);
      this.logger.debug('转换后Vis节点数:', visNodes.length);
      this.logger.debug('转换后Vis边数:', visEdges.length);
      this.logger.debug('边数据详情:', visEdges);

      // 更新数据
      this.nodes.clear();
      this.edges.clear();
      this.nodes.add(visNodes);
      this.edges.add(visEdges);
      this.logger.debug('数据已更新到Vis.js网络');

      // 适应视图
      setTimeout(() => {
        this.fit();
        this.logger.debug('视图适应完成');
      }, 100);

      this.logger.debug('Vis.js思维导图渲染完成');
    } catch (error) {
      this.logger.error('Vis.js思维导图渲染失败', error);
      this.renderErrorState(error);
    }
  }

  private parseMarkdownToNodes(markdown: string): NodeData[] {
    // 使用增强的解析逻辑
    const enhancedNodes = this.parseMarkdownToEnhancedNodes(markdown);

    // 转换为兼容的NodeData格式
    return this.convertEnhancedToNodeData(enhancedNodes);
  }

  private parseMarkdownToEnhancedNodes(markdown: string): EnhancedNodeData[] {
    const lines = markdown.split('\n');
    const rootNodes: EnhancedNodeData[] = [];
    const nodeStack: EnhancedNodeData[] = [];

    lines.forEach((line, index) => {
      if (!line.trim()) return;

      const structure = this.analyzeLineStructure(line, index, nodeStack);
      const node = this.createNodeFromStructure(structure, index);

      // 找到正确的父节点
      const parentNode = this.findParentNode(node, nodeStack);

      if (parentNode) {
        parentNode.children.push(node);
        node.parent = parentNode;
      } else {
        rootNodes.push(node);
      }

      // 更新节点栈
      this.updateNodeStack(node, nodeStack);
    });

    return rootNodes;
  }

  private analyzeLineStructure(line: string, lineIndex: number, nodeStack?: EnhancedNodeData[]): LineStructure {
    const structure: LineStructure = {
      level: 1,
      type: 'text',
      headingLevel: 0,
      listLevel: 0,
      indentLevel: 0,
      content: line.trim(),
      rawContent: line,
      styles: this.extractStyles(line)
    };

    // 标题识别 (H1-H6)
    const headingMatch = line.match(/^(#{1,6})\s+(.+)$/);
    if (headingMatch) {
      structure.type = 'heading';
      structure.headingLevel = headingMatch[1].length;
      structure.level = headingMatch[1].length;
      structure.content = headingMatch[2];
      return structure;
    }

    // 列表识别 (支持多种bullet符号和精确嵌套)
    const listMatch = line.match(/^(\s*)([•\-*+]|\d+\.)\s+(.+)$/);
    if (listMatch) {
      structure.type = 'list';
      structure.indentLevel = listMatch[1].length;
      structure.listLevel = Math.floor(listMatch[1].length / 2) + 1;
      structure.level = structure.listLevel + this.getParentHeadingLevel(nodeStack);
      structure.content = listMatch[3];
      return structure;
    }

    // 代码块识别
    if (line.trim().startsWith('```')) {
      structure.type = 'code';
      structure.level = this.getCurrentContextLevel(nodeStack);
      structure.content = line.trim();
      return structure;
    }

    // 引用识别
    if (line.trim().startsWith('>')) {
      structure.type = 'quote';
      structure.level = this.getCurrentContextLevel(nodeStack);
      structure.content = line.replace(/^\s*>\s*/, '');
      return structure;
    }

    return structure;
  }

  private extractStyles(content: string): StyleInfo {
    const styles: StyleInfo = {
      bold: false,
      italic: false,
      code: false,
      link: undefined,
      emoji: [],
      strikethrough: false,
      highlight: false
    };

    // 提取emoji
    const emojiRegex = /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/gu;
    const emojiMatches = content.match(emojiRegex);
    if (emojiMatches) {
      styles.emoji = emojiMatches;
    }

    // 检测粗体 **text** 或 __text__
    styles.bold = /\*\*.*?\*\*|__.*?__/.test(content);

    // 检测斜体 *text* 或 _text_
    styles.italic = /\*.*?\*|_.*?_/.test(content);

    // 检测代码 `code`
    styles.code = /`.*?`/.test(content);

    // 检测链接 [text](url)
    const linkMatch = content.match(/\[([^\]]+)\]\(([^)]+)\)/);
    if (linkMatch) {
      styles.link = linkMatch[2];
    }

    // 检测删除线 ~~text~~
    styles.strikethrough = /~~.*?~~/.test(content);

    // 检测高亮 ==text==
    styles.highlight = /==.*?==/.test(content);

    return styles;
  }

  private createNodeFromStructure(structure: LineStructure, lineIndex: number): EnhancedNodeData {
    return {
      id: this.generateId(),
      content: structure.content,
      rawContent: structure.rawContent,
      level: structure.level,
      type: structure.type,
      headingLevel: structure.headingLevel || undefined,
      listLevel: structure.listLevel || undefined,
      indentLevel: structure.indentLevel || undefined,
      styles: structure.styles,
      children: [],
      parent: undefined,
      position: { line: lineIndex, ch: 0 }
    };
  }

  private findParentNode(currentNode: EnhancedNodeData, nodeStack: EnhancedNodeData[]): EnhancedNodeData | null {
    // 从栈顶向下查找合适的父节点
    for (let i = nodeStack.length - 1; i >= 0; i--) {
      const candidate = nodeStack[i];
      if (candidate.level < currentNode.level) {
        return candidate;
      }
    }
    return null;
  }

  private updateNodeStack(node: EnhancedNodeData, nodeStack: EnhancedNodeData[]): void {
    // 移除层级大于等于当前节点的节点
    while (nodeStack.length > 0 && nodeStack[nodeStack.length - 1].level >= node.level) {
      nodeStack.pop();
    }

    // 添加当前节点到栈
    nodeStack.push(node);
  }

  private convertEnhancedToNodeData(enhancedNodes: EnhancedNodeData[]): NodeData[] {
    const convertNode = (enhanced: EnhancedNodeData): NodeData => {
      const node: NodeData = {
        id: enhanced.id,
        content: enhanced.content,
        level: enhanced.level,
        type: enhanced.type as 'heading' | 'list' | 'code' | 'ai-generated',
        children: enhanced.children.map(convertNode),
        position: enhanced.position
      };

      // 将增强节点存储到映射中以便后续使用
      this.nodeMap.set(node.id, node);
      this.enhancedNodeMap.set(enhanced.id, enhanced);
      return node;
    };

    return enhancedNodes.map(convertNode);
  }

  private getParentHeadingLevel(nodeStack?: EnhancedNodeData[]): number {
    if (!nodeStack || nodeStack.length === 0) return 1;

    // 从栈顶向下查找最近的标题节点
    for (let i = nodeStack.length - 1; i >= 0; i--) {
      const node = nodeStack[i];
      if (node.type === 'heading' && node.headingLevel) {
        return node.headingLevel;
      }
    }

    return 1;
  }

  private getCurrentContextLevel(nodeStack?: EnhancedNodeData[]): number {
    if (!nodeStack || nodeStack.length === 0) return 1;

    // 返回栈顶节点的层级 + 1
    const topNode = nodeStack[nodeStack.length - 1];
    return topNode.level + 1;
  }

  private convertToVisFormat(nodeData: NodeData[]): { visNodes: VisNode[], visEdges: VisEdge[] } {
    const visNodes: VisNode[] = [];
    const visEdges: VisEdge[] = [];

    const processNode = (node: NodeData, parentId?: string) => {
      // 创建Vis节点
      const visNode: VisNode = {
        id: node.id,
        label: this.formatNodeLabel(node),
        level: node.level,
        type: node.type,
        group: this.getNodeGroup(node),
        originalData: node
      };

      visNodes.push(visNode);

      // 创建边（如果有父节点）
      if (parentId) {
        const edge: VisEdge = {
          id: `edge-${parentId}-${node.id}`,
          from: parentId,
          to: node.id
        };
        visEdges.push(edge);
        this.logger.debug(`创建边: ${parentId} -> ${node.id}`);
      }

      // 递归处理子节点
      node.children.forEach(child => {
        processNode(child, node.id);
      });
    };

    nodeData.forEach(node => processNode(node));
    return { visNodes, visEdges };
  }

  private formatNodeLabel(node: NodeData): string {
    // 尝试获取增强的节点信息
    const enhancedNode = this.enhancedNodeMap.get(node.id);

    if (enhancedNode) {
      return this.formatEnhancedNodeLabel(enhancedNode);
    }

    // 回退到原始格式化逻辑
    let label = node.content;

    // 限制标签长度
    if (label.length > 50) {
      label = label.substring(0, 47) + '...';
    }

    // AI节点添加特殊标识
    if (node.type === 'ai-generated') {
      label = '🤖 ' + label;
    }

    return label;
  }

  private formatEnhancedNodeLabel(node: EnhancedNodeData): string {
    let label = node.content;

    // 保留emoji（已经在content中）
    // emoji在解析时已经保留在content中

    // 处理粗体（在Vis.js中用HTML标签）
    if (node.styles.bold) {
      // 移除原始的**标记，用HTML标签替换
      label = label.replace(/\*\*(.*?)\*\*/g, '<b>$1</b>');
      label = label.replace(/__(.*?)__/g, '<b>$1</b>');
    }

    // 处理斜体
    if (node.styles.italic) {
      // 移除原始的*标记，用HTML标签替换
      label = label.replace(/\*(.*?)\*/g, '<i>$1</i>');
      label = label.replace(/_(.*?)_/g, '<i>$1</i>');
    }

    // 处理代码
    if (node.styles.code) {
      // 移除原始的`标记，用HTML标签替换
      label = label.replace(/`(.*?)`/g, '<code>$1</code>');
    }

    // 处理删除线
    if (node.styles.strikethrough) {
      label = label.replace(/~~(.*?)~~/g, '<s>$1</s>');
    }

    // 长文本截断
    if (label.length > 50) {
      label = label.substring(0, 47) + '...';
    }

    return label;
  }

  private getNodeGroup(node: NodeData): string {
    if (node.type === 'heading') {
      return `heading${Math.min(node.level, 3)}`;
    }
    return node.type;
  }

  // 实现MindMapAPI接口方法
  updateNode(nodeId: string, data: Partial<NodeData>): void {
    const node = this.nodeMap.get(nodeId);
    if (!node) return;

    // 更新节点数据
    Object.assign(node, data);

    // 更新Vis节点
    const visNode = this.nodes.get(nodeId);
    if (visNode) {
      this.nodes.update({
        ...visNode,
        label: this.formatNodeLabel(node),
        group: this.getNodeGroup(node)
      });
    }

    if (this.onNodeChangeCallback) {
      this.onNodeChangeCallback(nodeId, data);
    }
  }

  addNode(parentId: string, data: NodeData): string {
    this.nodeMap.set(data.id, data);

    // 添加Vis节点
    const visNode: VisNode = {
      id: data.id,
      label: this.formatNodeLabel(data),
      level: data.level,
      type: data.type,
      group: this.getNodeGroup(data),
      originalData: data
    };

    this.nodes.add(visNode);

    // 添加边
    this.edges.add({
      from: parentId,
      to: data.id
    });

    if (this.onNodeChangeCallback) {
      this.onNodeChangeCallback(data.id, { action: 'add', parentId });
    }

    // 节点添加完成后，触发相关连接处理
    this.onNodeAdded(data.id);

    return data.id;
  }

  deleteNode(nodeId: string): void {
    this.nodeMap.delete(nodeId);
    this.nodes.remove(nodeId);

    // 删除相关的边
    const edgesToRemove = this.edges.get({
      filter: (edge: any) => edge.from === nodeId || edge.to === nodeId
    });

    this.edges.remove(edgesToRemove.map((edge: any) => edge.id!));

    if (this.onNodeChangeCallback) {
      this.onNodeChangeCallback(nodeId, { action: 'delete' });
    }
  }

  moveNode(nodeId: string, targetParentId: string): void {
    // 删除旧的边
    const oldEdges = this.edges.get({
      filter: (edge: any) => edge.to === nodeId
    });
    this.edges.remove(oldEdges.map((edge: any) => edge.id!));

    // 添加新的边
    this.edges.add({
      from: targetParentId,
      to: nodeId
    });

    if (this.onNodeChangeCallback) {
      this.onNodeChangeCallback(nodeId, { action: 'move', targetParentId });
    }
  }

  getNodeByPosition(_pos: { line: number; ch: number }): NodeData | null {
    // 暂时返回null，后续可以实现位置映射
    return null;
  }

  getPositionByNode(_nodeId: string): { line: number; ch: number } | null {
    // 暂时返回null，后续可以实现位置映射
    return null;
  }

  setNodeChangeCallback(callback: (nodeId: string, changes: any) => void): void {
    this.onNodeChangeCallback = callback;
  }

  fit(): void {
    if (this.network) {
      this.network.fit({
        animation: {
          duration: 500,
          easingFunction: 'easeInOutQuad'
        }
      });
    }
  }

  destroy(): void {
    // 清理主题监听器
    if (this.themeObserver) {
      this.themeObserver.disconnect();
      this.themeObserver = null;
      this.logger.debug('主题监听器已清理');
    }

    // 清理网络
    if (this.network) {
      this.network.destroy();
      this.network = null;
    }

    // 清理数据
    this.nodes.clear();
    this.edges.clear();
    this.nodeMap.clear();
    this.enhancedNodeMap.clear();
  }

  // 私有辅助方法
  private getLineLevel(line: string): number {
    const headingMatch = line.match(/^(#{1,6})\s/);
    if (headingMatch) return headingMatch[1].length;

    // 扩展支持: •, -, *, +, 数字列表
    const listMatch = line.match(/^(\s*)([•\-*+]|\d+\.)\s/);
    if (listMatch) return Math.floor(listMatch[1].length / 2) + 1;

    return 1;
  }

  private cleanLineContent(line: string): string {
    return line
      .replace(/^#{1,6}\s+/, '')
      .replace(/^\s*[•\-*+]\s+/, '')  // 扩展支持 • 符号
      .replace(/^\s*\d+\.\s+/, '')
      .trim();
  }

  private getNodeType(line: string): 'heading' | 'list' | 'code' | 'ai-generated' {
    if (line.match(/^#{1,6}\s/)) return 'heading';
    if (line.match(/^\s*[•\-*+]\s/)) return 'list';  // 扩展支持 • 符号
    if (line.match(/^\s*\d+\.\s/)) return 'list';
    if (line.includes('```')) return 'code';
    return 'list'; // 默认返回list而不是text
  }

  private generateId(): string {
    return Math.random().toString(36).substring(2, 11);
  }

  private handleNodeClick(nodeId: string): void {
    this.logger.debug('节点被点击', nodeId);

    // 如果在连接模式下，处理连接逻辑
    if (this.connectionMode === 'connecting') {
      this.handleConnectionModeClick(nodeId);
      return;
    }

    // 正常模式下的节点点击处理
    if (this.onNodeChangeCallback) {
      this.onNodeChangeCallback(nodeId, { action: 'click' });
    }
  }

  private handleNodeEdit(nodeId: string): void {
    this.logger.debug('节点被双击编辑', nodeId);
    if (this.onNodeChangeCallback) {
      this.onNodeChangeCallback(nodeId, { action: 'edit' });
    }
  }

  private handleNodeHover(_nodeId: string, _isHover: boolean): void {
    // 可以在这里添加悬停效果
  }

  private handleNodeDrag(nodeId: string, position: any): void {
    this.logger.debug('节点被拖拽', { nodeId, position });
    if (this.onNodeChangeCallback) {
      this.onNodeChangeCallback(nodeId, { action: 'drag', position });
    }
  }

  private renderEmptyState(): void {
    this.container.innerHTML = `
      <div class="mindmap-empty">
        <div class="empty-icon">🧠</div>
        <div class="empty-message">暂无内容</div>
        <div class="empty-hint">请在Markdown文档中添加标题或列表</div>
      </div>
    `;
  }

  private renderErrorState(error: any): void {
    this.container.innerHTML = `
      <div class="mindmap-error">
        <div class="error-icon">⚠️</div>
        <div class="error-message">思维导图渲染失败</div>
        <div class="error-detail">${error.message || '未知错误'}</div>
      </div>
    `;
  }

  // ==================== ExtendedMindMapAPI 接口实现 ====================

  /**
   * 添加逻辑连接
   */
  addLogicalConnection(connection: LogicalConnection): void {
    try {
      // 阶段1：数据格式验证
      if (!this.validateConnectionFormat(connection)) {
        this.logger.error('连接数据格式无效', connection);
        return;
      }

      this.connectionProcessingStats.formatValidationPassed++;
      this.logger.debug(`连接格式验证通过，尝试业务验证: ${connection.id}`);

      // 阶段2：尝试业务验证和立即处理
      if (this.validateConnectionBusiness(connection)) {
        // 业务验证通过，立即添加连接
        this.finalizeConnection(connection);
        this.connectionProcessingStats.businessValidationPassed++;
        this.connectionProcessingStats.processedCount++;
        this.logger.debug(`连接立即处理成功: ${connection.id}`);
      } else {
        // 业务验证失败，暂存到待处理队列
        this.pendingConnections.set(connection.id, connection);
        this.connectionProcessingStats.pendingCount++;
        this.logger.debug(`连接暂存到待处理队列: ${connection.id}, 待处理总数: ${this.pendingConnections.size}`);
      }

    } catch (error) {
      this.logger.error('添加连接失败', { connectionId: connection.id, error });
    }
  }

  /**
   * 最终确认并添加连接
   */
  private finalizeConnection(connection: LogicalConnection): void {
    // 添加到连接映射
    this.connectionMap.set(connection.id, connection);

    // 创建Vis.js边对象
    const visEdge = this.createVisEdgeFromConnection(connection);

    // 添加到逻辑连接数据集
    this.logicalConnections.add(visEdge);

    // 更新网络数据
    if (this.network) {
      const allEdges = [...this.edges.get(), ...this.logicalConnections.get()];
      this.network.setData({
        nodes: this.nodes,
        edges: allEdges
      });
    }

    // 触发连接事件
    if (this.onConnectionEventCallback) {
      this.onConnectionEventCallback('connection:create', {
        connectionId: connection.id,
        connection: connection
      });
    }

    this.logger.debug('逻辑连接已添加', connection.id);
  }

  /**
   * 删除逻辑连接
   */
  removeLogicalConnection(connectionId: string): void {
    try {
      const connection = this.connectionMap.get(connectionId);
      if (!connection) {
        this.logger.warn('连接不存在', connectionId);
        return;
      }

      // 从连接映射中删除
      this.connectionMap.delete(connectionId);

      // 从逻辑连接数据集中删除
      this.logicalConnections.remove(connectionId);

      // 更新网络数据
      if (this.network) {
        const allEdges = [...this.edges.get(), ...this.logicalConnections.get()];
        this.network.setData({
          nodes: this.nodes,
          edges: allEdges
        });
      }

      // 触发连接事件
      if (this.onConnectionEventCallback) {
        this.onConnectionEventCallback('connection:delete', {
          connectionId: connectionId,
          connection: connection
        });
      }

      this.logger.debug('逻辑连接已删除', connectionId);
    } catch (error) {
      this.logger.error('删除逻辑连接失败', error);
    }
  }

  /**
   * 更新逻辑连接
   */
  updateLogicalConnection(connectionId: string, updates: Partial<LogicalConnection>): void {
    try {
      const connection = this.connectionMap.get(connectionId);
      if (!connection) {
        this.logger.warn('连接不存在', connectionId);
        return;
      }

      // 更新连接对象
      const updatedConnection = { ...connection, ...updates, updatedAt: new Date() };
      this.connectionMap.set(connectionId, updatedConnection);

      // 更新Vis.js边对象
      const visEdge = this.createVisEdgeFromConnection(updatedConnection);
      this.logicalConnections.update(visEdge);

      // 更新网络数据
      if (this.network) {
        const allEdges = [...this.edges.get(), ...this.logicalConnections.get()];
        this.network.setData({
          nodes: this.nodes,
          edges: allEdges
        });
      }

      // 触发连接事件
      if (this.onConnectionEventCallback) {
        this.onConnectionEventCallback('connection:update', {
          connectionId: connectionId,
          connection: updatedConnection,
          changes: updates
        });
      }

      this.logger.debug('逻辑连接已更新', connectionId);
    } catch (error) {
      this.logger.error('更新逻辑连接失败', error);
    }
  }

  /**
   * 获取所有逻辑连接
   */
  getLogicalConnections(): LogicalConnection[] {
    return Array.from(this.connectionMap.values());
  }

  /**
   * 设置连接样式
   */
  setConnectionStyle(connectionId: string, style: any): void {
    this.updateLogicalConnection(connectionId, { style });
  }

  /**
   * 设置连接颜色
   */
  setConnectionColor(connectionId: string, color: string): void {
    this.updateLogicalConnection(connectionId, { color });
  }

  /**
   * 设置连接标签
   */
  setConnectionLabel(connectionId: string, label: string): void {
    this.updateLogicalConnection(connectionId, { label });
  }

  /**
   * 设置连接模式
   */
  setConnectionMode(mode: ConnectionMode): void {
    this.connectionMode = mode;
    this.selectedSourceNode = null; // 重置选择状态

    // 更新UI状态提示
    this.updateConnectionModeUI();
  }

  /**
   * 获取连接模式
   */
  getConnectionMode(): ConnectionMode {
    return this.connectionMode;
  }

  /**
   * 设置连接事件回调
   */
  setConnectionEventCallback(callback: (event: ConnectionEventType, data: ConnectionEventData) => void): void {
    this.onConnectionEventCallback = callback;
  }

  // ==================== 连接相关的辅助方法 ====================

  /**
   * 验证连接数据格式
   * 检查必需字段和数据类型，不检查业务逻辑
   */
  private validateConnectionFormat(connection: LogicalConnection): boolean {
    // 检查必要字段
    if (!connection.id || !connection.fromNodeId || !connection.toNodeId) {
      this.logger.debug('连接格式验证失败：缺少必需字段', {
        id: !!connection.id,
        fromNodeId: !!connection.fromNodeId,
        toNodeId: !!connection.toNodeId
      });
      return false;
    }

    // 检查字段类型
    if (typeof connection.id !== 'string' ||
        typeof connection.fromNodeId !== 'string' ||
        typeof connection.toNodeId !== 'string') {
      this.logger.debug('连接格式验证失败：字段类型错误', connection);
      return false;
    }

    this.logger.debug('连接格式验证通过', connection.id);
    return true;
  }

  /**
   * 验证连接业务逻辑
   * 检查节点存在性、避免重复连接等业务规则
   */
  private validateConnectionBusiness(connection: LogicalConnection): boolean {
    // 检查源节点和目标节点是否存在
    if (!this.nodeMap.has(connection.fromNodeId)) {
      this.logger.debug(`连接业务验证失败：源节点不存在 ${connection.fromNodeId}`, connection.id);
      return false;
    }

    if (!this.nodeMap.has(connection.toNodeId)) {
      this.logger.debug(`连接业务验证失败：目标节点不存在 ${connection.toNodeId}`, connection.id);
      return false;
    }

    // 检查是否为自连接
    if (connection.fromNodeId === connection.toNodeId) {
      this.logger.debug('连接业务验证失败：自连接', connection.id);
      return false;
    }

    // 检查是否已存在相同的连接
    const existingConnection = Array.from(this.connectionMap.values()).find(
      conn => conn.fromNodeId === connection.fromNodeId && conn.toNodeId === connection.toNodeId
    );
    if (existingConnection && existingConnection.id !== connection.id) {
      this.logger.debug('连接业务验证失败：重复连接', connection.id);
      return false;
    }

    this.logger.debug('连接业务验证通过', connection.id);
    return true;
  }

  /**
   * 验证连接的有效性（保留原接口，用于向后兼容）
   * @deprecated 建议使用 validateConnectionFormat 和 validateConnectionBusiness
   */
  private validateConnection(connection: LogicalConnection): boolean {
    return this.validateConnectionFormat(connection) && this.validateConnectionBusiness(connection);
  }

  /**
   * 从LogicalConnection创建Vis.js边对象
   */
  private createVisEdgeFromConnection(connection: LogicalConnection): any {
    return {
      id: connection.id,
      from: connection.fromNodeId,
      to: connection.toNodeId,
      label: connection.label || '',
      color: {
        color: connection.color,
        opacity: 0.8
      },
      width: connection.width,
      arrows: {
        to: {
          enabled: true,
          type: this.getArrowType(connection.arrowType)
        }
      },
      dashes: this.getConnectionDashes(connection.style),
      smooth: {
        enabled: true,
        type: 'cubicBezier',
        roundness: 0.3
      },
      font: {
        size: 12,
        color: connection.color,
        strokeWidth: 0
      },
      // 标记为逻辑连接
      isLogical: true
    };
  }

  /**
   * 获取箭头类型
   */
  private getArrowType(arrowType: any): string {
    switch (arrowType) {
      case 'bold': return 'arrow';
      case 'triangle': return 'triangle';
      case 'curved': return 'curve';
      default: return 'arrow';
    }
  }

  /**
   * 获取连接线样式
   */
  private getConnectionDashes(style: any): boolean | number[] {
    switch (style) {
      case 'dashed': return [5, 5];
      case 'dotted': return [2, 3];
      case 'bold': return false;
      default: return false;
    }
  }

  /**
   * 更新连接模式UI状态
   */
  private updateConnectionModeUI(): void {
    // 移除之前的状态类
    this.container.classList.remove('connection-mode-normal', 'connection-mode-connecting', 'connection-mode-editing');

    // 添加当前状态类
    this.container.classList.add(`connection-mode-${this.connectionMode}`);

    // 更新鼠标样式
    switch (this.connectionMode) {
      case 'connecting':
        this.container.style.cursor = 'crosshair';
        break;
      case 'editing':
        this.container.style.cursor = 'pointer';
        break;
      default:
        this.container.style.cursor = 'default';
        break;
    }
  }

  /**
   * 处理连接模式下的节点点击
   */
  private handleConnectionModeClick(nodeId: string): void {
    if (this.connectionMode !== 'connecting') {
      return;
    }

    if (!this.selectedSourceNode) {
      // 选择源节点
      this.selectedSourceNode = nodeId;
      this.highlightNode(nodeId, true);

      if (this.onConnectionEventCallback) {
        this.onConnectionEventCallback('connection:select', {
          connectionId: '',
          position: { x: 0, y: 0 }
        });
      }
    } else if (this.selectedSourceNode !== nodeId) {
      // 选择目标节点，创建连接
      this.createConnectionBetweenNodes(this.selectedSourceNode, nodeId);

      // 重置状态
      this.highlightNode(this.selectedSourceNode, false);
      this.selectedSourceNode = null;
      this.setConnectionMode('normal');
    }
  }

  /**
   * 在两个节点之间创建连接
   */
  private createConnectionBetweenNodes(fromNodeId: string, toNodeId: string): void {
    const connection: LogicalConnection = {
      id: this.generateConnectionId(),
      fromNodeId: fromNodeId,
      toNodeId: toNodeId,
      type: 'logical',
      style: 'solid',
      color: '#666666',
      width: 2,
      arrowType: 'normal',
      createdAt: new Date()
    };

    this.addLogicalConnection(connection);
  }

  /**
   * 生成连接ID
   */
  private generateConnectionId(): string {
    return 'conn-' + Math.random().toString(36).substring(2, 11);
  }

  /**
   * 高亮节点
   */
  private highlightNode(nodeId: string, highlight: boolean): void {
    try {
      const node = this.nodes.get(nodeId);
      if (node) {
        const updatedNode = {
          ...node,
          borderWidth: highlight ? 3 : 1,
          borderWidthSelected: highlight ? 4 : 2
        };
        this.nodes.update(updatedNode);
      }
    } catch (error) {
      this.logger.error('高亮节点失败', error);
    }
  }

  // ==================== 待处理连接管理机制 ====================

  /**
   * 处理所有待处理的连接
   * 通常在节点加载完成后调用
   */
  public processPendingConnections(): void {
    if (this.pendingConnections.size === 0) {
      this.logger.debug('没有待处理的连接');
      return;
    }

    this.logger.debug(`开始处理 ${this.pendingConnections.size} 个待处理连接`);
    const processedIds: string[] = [];

    for (const [connectionId, connection] of this.pendingConnections) {
      if (this.processPendingConnection(connectionId)) {
        processedIds.push(connectionId);
      }
    }

    this.logger.debug(`成功处理 ${processedIds.length} 个连接，剩余 ${this.pendingConnections.size} 个待处理`);
  }

  /**
   * 处理单个待处理连接
   */
  private processPendingConnection(connectionId: string): boolean {
    const connection = this.pendingConnections.get(connectionId);
    if (!connection) {
      this.logger.debug(`待处理连接不存在: ${connectionId}`);
      return false;
    }

    // 重新进行业务验证
    if (this.validateConnectionBusiness(connection)) {
      // 验证通过，正式添加连接
      this.finalizeConnection(connection);
      this.pendingConnections.delete(connectionId);
      this.connectionProcessingStats.businessValidationPassed++;
      this.connectionProcessingStats.processedCount++;
      this.connectionProcessingStats.pendingCount--;

      this.logger.debug(`待处理连接处理成功: ${connectionId}`);
      return true;
    } else {
      this.logger.debug(`待处理连接仍未满足条件: ${connectionId}`);
      return false;
    }
  }

  /**
   * 获取连接处理统计信息
   */
  public getConnectionProcessingStats(): any {
    return {
      ...this.connectionProcessingStats,
      currentPendingCount: this.pendingConnections.size,
      pendingConnectionIds: Array.from(this.pendingConnections.keys())
    };
  }

  /**
   * 节点添加后的处理
   * 检查并处理等待此节点的连接
   */
  private onNodeAdded(nodeId: string): void {
    if (this.pendingConnections.size === 0) {
      return;
    }

    this.logger.debug(`节点添加完成，检查相关连接: ${nodeId}`);
    let processedCount = 0;

    // 检查是否有等待此节点的连接
    for (const [connectionId, connection] of this.pendingConnections) {
      if (connection.fromNodeId === nodeId || connection.toNodeId === nodeId) {
        if (this.processPendingConnection(connectionId)) {
          processedCount++;
        }
      }
    }

    if (processedCount > 0) {
      this.logger.debug(`节点 ${nodeId} 相关的 ${processedCount} 个连接已处理`);
    }
  }
}
